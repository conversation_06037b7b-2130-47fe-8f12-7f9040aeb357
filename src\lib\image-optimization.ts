/**
 * Image optimization utilities to improve web performance
 */

import { useState, useEffect } from 'react';

/**
 * Creates a low-quality image placeholder URL
 * @param src The original image source
 * @param width Width of the LQIP
 * @param quality Quality of the LQIP (1-100)
 * @returns A data URL for the low-quality image
 */
export async function createLQIP(src: string, _width = 20, _quality = 20): Promise<string> {
  // This function would normally use server-side processing
  // For client-side, we'll return a simpler placeholder
  return src;
}

/**
 * Hook to lazy load images with IntersectionObserver
 * @param elementRef Reference to the element to observe
 * @param options IntersectionObserver options
 * @returns Whether the element is visible
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = { threshold: 0.1, rootMargin: '100px' }
): boolean {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!elementRef.current) return;

    // Store a reference to the current element to avoid closure issues
    const currentElement = elementRef.current;

    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);

    observer.observe(currentElement);

    return () => {
      observer.unobserve(currentElement);
    };
  }, [elementRef, options]);

  return isVisible;
}

/**
 * Optimized image component with lazy loading, LQIP and proper sizing
 */
// Removed unused ImageLoaderProps type to fix linting warning
// type ImageLoaderProps = {
//   src: string;
//   width?: number;
//   height?: number;
//   alt: string;
//   priority?: boolean;
//   className?: string;
//   onLoad?: () => void;
// };

export function generateSrcSet(src: string, sizes: number[] = [400, 800, 1200, 1600]): string {
  // For static images without a CDN, we'll return an empty srcset to avoid browser errors
  // This prevents "Failed parsing 'srcset' attribute value" errors
  // In production, you would use a service like Cloudinary or ImageKit for responsive images

  // Check if the src is a static asset (starts with / or ./public)
  if (src.startsWith('/') || src.startsWith('./')) {
    // Return empty srcset for static assets to prevent parsing errors
    return '';
  }

  // For external URLs or CDN images, generate proper srcset
  // Ensure the src is properly encoded before generating srcset
  const encodedSrc = encodeURI(src);
  const srcSet = sizes.map(size => {
    return `${encodedSrc}?w=${size} ${size}w`;
  }).join(', ');

  return srcSet;
}

/**
 * Checks if WebP format is supported by the browser
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Gets the optimal image format based on browser support
 */
export async function getOptimalImageFormat(src: string): Promise<string> {
  const isWebPSupported = await supportsWebP();

  if (isWebPSupported && !src.includes('.svg')) {
    // Convert to WebP if supported and not SVG
    return src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }

  return src;
}

/**
 * Preloads critical images to improve LCP with proper validation
 * @param imagePaths Array of image paths to preload
 */
export async function preloadCriticalImages(imagePaths: string[]): Promise<void> {
  if (typeof window === 'undefined') return;

  // Validate images first to avoid preloading non-existent resources
  const validationResults = await validateImageUrls(imagePaths);

  validationResults.forEach(({ url, isValid }) => {
    if (!isValid) {
      console.warn(`Skipping preload for invalid image: ${url}`);
      return;
    }

    try {
      // Check if already preloaded - use consistent encoding
      const encodedPath = encodeURI(url);
      if (document.querySelector(`link[rel="preload"][href="${encodedPath}"]`)) {
        return;
      }

      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = encodedPath;

      // Only add crossorigin for external images or when needed
      if (encodedPath.startsWith('http') || encodedPath.includes('lovable-uploads')) {
        link.crossOrigin = 'anonymous';
      }

      document.head.appendChild(link);
    } catch (error) {
      console.warn(`Failed to preload image ${url}:`, error);
    }
  });
}

/**
 * Validates if an image URL is accessible
 * @param src Image source URL
 * @returns Promise that resolves to true if image loads successfully
 */
export function validateImageUrl(src: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    const encodedSrc = src.includes(' ') ? encodeURI(src) : src;

    img.onload = () => resolve(true);
    img.onerror = () => {
      console.warn('Image validation failed for:', encodedSrc);
      resolve(false);
    };

    img.src = encodedSrc;
  });
}

/**
 * Batch validates multiple image URLs
 * @param urls Array of image URLs to validate
 * @returns Promise that resolves to an array of validation results
 */
export async function validateImageUrls(urls: string[]): Promise<{ url: string; isValid: boolean }[]> {
  const validationPromises = urls.map(async (url) => ({
    url,
    isValid: await validateImageUrl(url)
  }));

  return Promise.all(validationPromises);
}
