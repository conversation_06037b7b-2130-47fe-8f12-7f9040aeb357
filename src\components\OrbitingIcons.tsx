import React from 'react';
import { motion } from 'framer-motion';
import {
  Code,
  Camera,
  Palette,
  Monitor,
  Smartphone,
  Aperture,
  Lightbulb,
  Star,
  Diamond,
  Wifi,
  Cloud,
  <PERSON>Pointer,
  Zap,
  Sparkles,
  // Additional icons for expanded paths
  Brush,
  Cpu,
  Database,
  Globe,
  Headphones,
  Image,
  Layers,
  Mic,
  Music,
  Play,
  Rocket,
  Search,
  Settings,
  Shield,
  Target,
  Tv,
  Users,
  Video,
  Wand2,
  Wrench,
  Eye,
  Heart
} from 'lucide-react';

// ENHANCED PROMINENCE 4-Path Orbital System - Significantly larger and more visible for maximum impact
const orbitingIcons: IconConfig[] = [
  // Inner Path - Clockwise rotation (35s) - Core Creative Elements (6 icons, 60° spacing) - ENHANCED PROMINENCE
  { Icon: Diamond, color: 'text-premium-platinum/70', size: 42, radius: 280, sector: 0, orbitalFamily: 'inner' },
  { Icon: Lightbulb, color: 'text-premium-gold/65', size: 45, radius: 280, sector: 60, orbitalFamily: 'inner' },
  { Icon: Sparkles, color: 'text-neon-cyan/65', size: 42, radius: 280, sector: 120, orbitalFamily: 'inner' },
  { Icon: Star, color: 'text-neon-purple/65', size: 44, radius: 280, sector: 180, orbitalFamily: 'inner' },
  { Icon: Heart, color: 'text-neon-pink/65', size: 41, radius: 280, sector: 240, orbitalFamily: 'inner' },
  { Icon: Wand2, color: 'text-premium-gold/60', size: 42, radius: 280, sector: 300, orbitalFamily: 'inner' },

  // Middle Path - Counterclockwise rotation (50s) - Technology & Design Focus (8 icons, 45° spacing) - ENHANCED PROMINENCE
  { Icon: Camera, color: 'text-neon-purple/60', size: 48, radius: 392, sector: 0, orbitalFamily: 'middle' },
  { Icon: Code, color: 'text-neon-cyan/60', size: 51, radius: 392, sector: 45, orbitalFamily: 'middle' },
  { Icon: Palette, color: 'text-neon-blue/60', size: 48, radius: 392, sector: 90, orbitalFamily: 'middle' },
  { Icon: Aperture, color: 'text-premium-gold/60', size: 47, radius: 392, sector: 135, orbitalFamily: 'middle' },
  { Icon: Zap, color: 'text-neon-green/60', size: 50, radius: 392, sector: 180, orbitalFamily: 'middle' },
  { Icon: Brush, color: 'text-neon-orange/60', size: 45, radius: 392, sector: 225, orbitalFamily: 'middle' },
  { Icon: Layers, color: 'text-premium-sapphire/60', size: 48, radius: 392, sector: 270, orbitalFamily: 'middle' },
  { Icon: Image, color: 'text-neon-purple/55', size: 47, radius: 392, sector: 315, orbitalFamily: 'middle' },

  // Outer Path - Clockwise rotation (70s) - Professional Tools (10 icons, 36° spacing) - ENHANCED PROMINENCE
  { Icon: Monitor, color: 'text-premium-sapphire/55', size: 45, radius: 504, sector: 0, orbitalFamily: 'outer' },
  { Icon: Smartphone, color: 'text-neon-green/55', size: 42, radius: 504, sector: 36, orbitalFamily: 'outer' },
  { Icon: Cloud, color: 'text-premium-silver/55', size: 45, radius: 504, sector: 72, orbitalFamily: 'outer' },
  { Icon: Wifi, color: 'text-neon-cyan/55', size: 44, radius: 504, sector: 108, orbitalFamily: 'outer' },
  { Icon: MousePointer, color: 'text-neon-blue/55', size: 42, radius: 504, sector: 144, orbitalFamily: 'outer' },
  { Icon: Database, color: 'text-premium-platinum/55', size: 45, radius: 504, sector: 180, orbitalFamily: 'outer' },
  { Icon: Globe, color: 'text-neon-green/50', size: 44, radius: 504, sector: 216, orbitalFamily: 'outer' },
  { Icon: Settings, color: 'text-premium-silver/50', size: 42, radius: 504, sector: 252, orbitalFamily: 'outer' },
  { Icon: Rocket, color: 'text-neon-orange/55', size: 45, radius: 504, sector: 288, orbitalFamily: 'outer' },
  { Icon: Shield, color: 'text-premium-sapphire/50', size: 44, radius: 504, sector: 324, orbitalFamily: 'outer' },

  // Ultra Path - Counterclockwise rotation (100s) - Ambient Professional Elements (12 icons, 30° spacing) - ENHANCED PROMINENCE
  { Icon: Cpu, color: 'text-premium-platinum/45', size: 39, radius: 616, sector: 0, orbitalFamily: 'ultra' },
  { Icon: Headphones, color: 'text-neon-cyan/45', size: 38, radius: 616, sector: 30, orbitalFamily: 'ultra' },
  { Icon: Mic, color: 'text-premium-gold/45', size: 36, radius: 616, sector: 60, orbitalFamily: 'ultra' },
  { Icon: Music, color: 'text-neon-purple/45', size: 39, radius: 616, sector: 90, orbitalFamily: 'ultra' },
  { Icon: Play, color: 'text-neon-green/45', size: 38, radius: 616, sector: 120, orbitalFamily: 'ultra' },
  { Icon: Search, color: 'text-premium-silver/45', size: 36, radius: 616, sector: 150, orbitalFamily: 'ultra' },
  { Icon: Target, color: 'text-neon-blue/45', size: 39, radius: 616, sector: 180, orbitalFamily: 'ultra' },
  { Icon: Tv, color: 'text-premium-sapphire/45', size: 38, radius: 616, sector: 210, orbitalFamily: 'ultra' },
  { Icon: Users, color: 'text-neon-orange/45', size: 36, radius: 616, sector: 240, orbitalFamily: 'ultra' },
  { Icon: Video, color: 'text-premium-gold/40', size: 39, radius: 616, sector: 270, orbitalFamily: 'ultra' },
  { Icon: Wrench, color: 'text-premium-silver/40', size: 38, radius: 616, sector: 300, orbitalFamily: 'ultra' },
  { Icon: Eye, color: 'text-neon-cyan/40', size: 36, radius: 616, sector: 330, orbitalFamily: 'ultra' }
];

// ENHANCED PROMINENCE 4-Path Orbital Configuration - Significantly larger radii for maximum visual dominance
const orbitalConfig: Record<OrbitFamily, {
  baseSpeed: number;
  direction: number;
  phaseOffset: number;
  iconCount: number;
  radius: number;
}> = {
  inner: {
    baseSpeed: 35,      // Energetic, core rotation
    direction: 1,       // Clockwise (primary direction)
    phaseOffset: 0,     // Primary reference point
    iconCount: 6,       // 6 icons, 60° spacing
    radius: 280         // ENHANCED: 40% larger (200px → 280px) for maximum prominence
  },
  middle: {
    baseSpeed: 50,      // Balanced medium speed
    direction: -1,      // Counterclockwise (creates visual contrast)
    phaseOffset: 30,    // 30° offset for harmonic spacing
    iconCount: 8,       // 8 icons, 45° spacing
    radius: 392         // ENHANCED: 40% larger (280px → 392px) for maximum prominence
  },
  outer: {
    baseSpeed: 70,      // Professional, steady rotation
    direction: 1,       // Clockwise (harmonizes with inner)
    phaseOffset: 18,    // 18° offset for coordinated positioning
    iconCount: 10,      // 10 icons, 36° spacing
    radius: 504         // ENHANCED: 40% larger (360px → 504px) for maximum prominence
  },
  ultra: {
    baseSpeed: 100,     // Slow, stately background rotation
    direction: -1,      // Counterclockwise (alternating pattern)
    phaseOffset: 15,    // 15° offset for subtle depth
    iconCount: 12,      // 12 icons, 30° spacing
    radius: 616         // ENHANCED: 40% larger (440px → 616px) for maximum prominence
  }
};

// Enhanced sector management for 4-path system - ensures balanced distribution and harmonic spacing
const calculateSectorPosition = (sector: number, familyPhaseOffset: number, _iconCount: number) => {
  // Dynamic sector calculation based on icon count per path
  // Inner: 6 icons = 60° spacing, Middle: 8 icons = 45° spacing, Outer: 10 icons = 36° spacing, Ultra: 12 icons = 30° spacing
  // The sector spacing is calculated as: 360 / _iconCount

  // Add family phase offset for coordinated movement and harmonic relationships
  return (sector + familyPhaseOffset) % 360;
};

// Define the type for orbit family
type OrbitFamily = 'inner' | 'middle' | 'outer' | 'ultra';

// Define the type for the icon configuration
interface IconConfig {
  Icon: React.ElementType;
  color: string;
  size: number;
  radius: number;
  sector: number;
  orbitalFamily: string;
}

// Enhanced positioning system with continuous-running appearance and coordinated timing
const calculateOrganizedState = (icon: IconConfig, iconIndex: number) => {
  // Type assertion to make TypeScript happy with the string indexing
  const config = orbitalConfig[icon.orbitalFamily as OrbitFamily];

  // Calculate organized initial position based on sector and path configuration
  const sectorPosition = calculateSectorPosition(icon.sector, config.phaseOffset, config.iconCount);

  // Create staggered initial positions for continuous-running appearance
  // Each path starts at different points in their cycle to appear already in motion
  let continuousOffset = 0;
  switch (icon.orbitalFamily) {
    case 'inner': continuousOffset = 45; break;    // Start 45° into cycle
    case 'middle': continuousOffset = 135; break;  // Start 135° into cycle
    case 'outer': continuousOffset = 225; break;   // Start 225° into cycle
    case 'ultra': continuousOffset = 315; break;   // Start 315° into cycle (full stagger)
    default: continuousOffset = 0;
  }

  // Add subtle variation (±6 degrees) to prevent mechanical perfection while maintaining organization
  const organicVariation = (Math.sin(iconIndex * 2.1) * 6); // Deterministic but organic
  const initialRotation = sectorPosition + continuousOffset + organicVariation;

  return {
    initialRotation,
    actualSpeed: config.baseSpeed,
    direction: config.direction,
    pathRadius: config.radius // Use radius from config for consistency
  };
};

// Smooth, coordinated orbital animation variants
const createOrbitVariants = (speed: number, initialRotation: number, direction: number) => ({
  initial: {
    rotate: initialRotation,
  },
  animate: {
    rotate: initialRotation + (360 * direction),
    transition: {
      duration: speed,
      repeat: Infinity,
      ease: "linear",
      repeatType: "loop" as const
    }
  }
});

// Enhanced floating animation that complements the 4-path orbital motion
const createFloatingVariants = (orbitalFamily: string, iconIndex: number) => {
  // Coordinated floating timing optimized for each path
  const getFloatingConfig = (family: string) => {
    switch (family) {
      case 'inner': return { duration: 5.2, amplitude: 2.0, scaleRange: 1.04 };   // Energetic floating for inner path
      case 'middle': return { duration: 6.8, amplitude: 2.4, scaleRange: 1.03 };  // Dynamic floating for middle path
      case 'outer': return { duration: 8.5, amplitude: 1.8, scaleRange: 1.025 };  // Balanced floating for outer path
      case 'ultra': return { duration: 11.2, amplitude: 1.4, scaleRange: 1.015 }; // Subtle floating for ultra path
      default: return { duration: 7, amplitude: 2, scaleRange: 1.025 };
    }
  };
  
  const floatingConfig = getFloatingConfig(orbitalFamily);

  // Create harmonic delay pattern based on path position
  const getHarmonicDelay = (family: string, index: number) => {
    switch (family) {
      case 'inner': return index * 0.35;   // Quick succession for inner path
      case 'middle': return index * 0.45;  // Medium-quick staggering for middle path
      case 'outer': return index * 0.6;    // Balanced staggering for outer path
      case 'ultra': return index * 0.8;    // Gentle staggering for ultra path
      default: return index * 0.5;
    }
  };
  
  const harmonicDelay = getHarmonicDelay(orbitalFamily, iconIndex);

  // ENHANCED PROMINENCE opacity ranges - Significantly more visible (0.8-1.0 range)
  const getOpacityRange = (family: string): [number, number, number] => {
    switch (family) {
      case 'inner': return [0.95, 1.0, 0.95];     // ENHANCED: Maximum visibility for inner path
      case 'middle': return [0.9, 0.98, 0.9];     // ENHANCED: High visibility for middle path
      case 'outer': return [0.85, 0.95, 0.85];    // ENHANCED: Strong visibility for outer path
      case 'ultra': return [0.8, 0.9, 0.8];       // ENHANCED: Good visibility for ultra path
      default: return [0.85, 0.95, 0.85];
    }
  };
  
  const opacityRange = getOpacityRange(orbitalFamily);

  return {
    initial: {
      y: 0,
      scale: 1,
      opacity: opacityRange[0]
    },
    animate: {
      y: [-floatingConfig.amplitude, floatingConfig.amplitude, -floatingConfig.amplitude],
      scale: [1, floatingConfig.scaleRange, 1],
      opacity: opacityRange,
      transition: {
        duration: floatingConfig.duration,
        repeat: Infinity,
        ease: "easeInOut",
        delay: harmonicDelay
      }
    }
  };
};

// Subtle, organized pulsing animation for accent elements
const createPulseVariants = (familyIndex: number) => {
  return {
    initial: {
      scale: 1,
      opacity: 0.8
    },
    animate: {
      scale: [1, 1.05, 1],
      opacity: [0.8, 0.9, 0.8],
      transition: {
        duration: 5,
        repeat: Infinity,
        ease: "easeInOut",
        delay: familyIndex * 1.2 // Coordinated delays
      }
    }
  };
};

interface OrbitingIconsProps {
  className?: string;
}

const OrbitingIcons: React.FC<OrbitingIconsProps> = ({ className = '' }) => {
  return (
    <div
      className={`absolute inset-0 pointer-events-none overflow-hidden ${className}`}
      aria-hidden="true"
      role="presentation"
    >
      {/* Enhanced responsive visibility with accessibility support and performance optimization */}
      <div className="hidden md:block motion-safe:block motion-reduce:hidden">
        {/* Enhanced 4-Path Orbital System with coordinated animations - reduced animation load */}
        {orbitingIcons.filter((_, idx) => idx % 2 === 0).map((config, index) => {
          const { Icon, color, size, orbitalFamily, sector } = config;
          // We'll use pathRadius from the calculated state instead of the config radius

          // Calculate enhanced orbital mechanics for this icon
          const { initialRotation, actualSpeed, direction, pathRadius } =
            calculateOrganizedState(config, index);

          // Enhanced z-index layering for 4-path system
          const zIndexMap = {
            inner: 4,
            middle: 3,
            outer: 2,
            ultra: 1
          };

          // ENHANCED PROMINENCE base opacity - Significantly more visible across all paths
          const baseOpacity = {
            inner: 1.0,      // ENHANCED: Maximum visibility for inner path
            middle: 0.95,    // ENHANCED: High visibility for middle path
            outer: 0.9,      // ENHANCED: Strong visibility for outer path
            ultra: 0.85      // ENHANCED: Good visibility for ultra path
          }[orbitalFamily] || 0.9;

          return (
            <motion.div
              key={`${orbitalFamily}-${sector}-${index}`}
              className={`orbital-icon absolute top-1/2 left-1/2 ${color}`}
              style={{
                width: pathRadius * 2,
                height: pathRadius * 2,
                marginLeft: -pathRadius,
                marginTop: -pathRadius,
                willChange: 'transform',
                transform: 'translate3d(0, 0, 0)', // Hardware acceleration
                zIndex: zIndexMap[orbitalFamily as keyof typeof zIndexMap]
              }}
              variants={createOrbitVariants(actualSpeed, initialRotation, direction)}
              initial="initial"
              animate="animate"
            >
              <motion.div
                className="absolute"
                style={{
                  top: 0, // Positioned on outer edge of orbital path
                  left: '50%',
                  transform: 'translateX(-50%)',
                  willChange: 'transform'
                }}
                variants={createFloatingVariants(orbitalFamily, index)}
                initial="initial"
                animate="animate"
              >
                <Icon
                  size={size}
                  className="drop-shadow-lg transition-opacity duration-300 filter blur-0 hover:opacity-100"
                  style={{
                    willChange: 'opacity',
                    opacity: baseOpacity
                  }}
                />
              </motion.div>
            </motion.div>
          );
        })}
      </div>

      {/* Enhanced mobile version with optimized orbital system */}
      <div className="block md:hidden motion-safe:block motion-reduce:hidden">
        {/* Mobile-optimized 2-path orbital system with reduced complexity */}
        {orbitingIcons
          .filter(config => 
            // Only use inner and middle paths on mobile, and select subset of icons for performance
            (config.orbitalFamily === 'inner' && config.sector % 120 === 0) || 
            (config.orbitalFamily === 'middle' && config.sector % 90 === 0)
          )
          .map((config, index) => {
            const { Icon, color, size: originalSize, orbitalFamily, sector } = config;
            
            // Scaled-down size for mobile
            const size = Math.max(Math.floor(originalSize * 0.65), 18);
            
            // Scaled-down radius for mobile
            const mobileRadiusMap = {
              inner: 120, // Smaller inner radius for mobile
              middle: 200, // Smaller middle radius for mobile
            };
            const radius = mobileRadiusMap[orbitalFamily as keyof typeof mobileRadiusMap] || 120;
            
            // Use faster animation for mobile
            const speedMultiplier = 1.5; // Faster on mobile for perceived performance
            const { initialRotation, actualSpeed, direction } = calculateOrganizedState(config, index);
            
            return (
              <motion.div
                key={`mobile-${orbitalFamily}-${sector}-${index}`}
                className={`orbital-icon absolute top-1/2 left-1/2 ${color}`}
                style={{
                  width: radius * 2,
                  height: radius * 2,
                  marginLeft: -radius,
                  marginTop: -radius,
                  willChange: 'transform',
                  transform: 'translate3d(0, 0, 0)', // Hardware acceleration
                }}
                variants={createOrbitVariants(actualSpeed / speedMultiplier, initialRotation, direction)}
                initial="initial"
                animate="animate"
              >
                <motion.div
                  className="absolute"
                  style={{
                    top: 0,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    willChange: 'transform'
                  }}
                  variants={createFloatingVariants(orbitalFamily, index)}
                  initial="initial"
                  animate="animate"
                >
                  <Icon
                    size={size}
                    strokeWidth={2}
                    className="drop-shadow-lg transition-opacity duration-300"
                    style={{
                      willChange: 'opacity',
                      opacity: 0.9 // Higher opacity on mobile for better visibility
                    }}
                  />
                </motion.div>
              </motion.div>
            );
        })}
        
        {/* Supplemental mobile floating elements for additional visual interest */}
        <motion.div
          className="absolute top-1/4 right-1/4 w-3 h-3 sm:w-4 sm:h-4 bg-premium-gold rounded-full opacity-60"
          variants={createPulseVariants(0)}
          initial="initial"
          animate="animate"
          style={{ willChange: 'transform' }}
        />

        <motion.div
          className="absolute bottom-1/3 left-1/4 w-2 h-2 sm:w-3 sm:h-3 bg-neon-cyan rounded-full opacity-50"
          variants={createPulseVariants(1)}
          initial="initial"
          animate="animate"
          style={{ willChange: 'transform' }}
        />

        <motion.div
          className="absolute top-1/2 right-1/3 w-2.5 h-2.5 sm:w-3.5 sm:h-3.5 bg-neon-purple rounded-full opacity-55"
          variants={createPulseVariants(2)}
          initial="initial"
          animate="animate"
          style={{ willChange: 'transform' }}
        />
      </div>

      {/* Enhanced scattered floating elements - Desktop only with coordinated timing */}
      <div className="hidden md:block motion-safe:block motion-reduce:hidden">
        <motion.div
          className="absolute top-1/4 left-1/4 w-2.5 h-2.5 bg-premium-gold rounded-full opacity-60"
          variants={createPulseVariants(0)}
          initial="initial"
          animate="animate"
          style={{ willChange: 'transform' }}
        />

        <motion.div
          className="absolute top-3/4 right-1/3 w-2 h-2 bg-neon-cyan rounded-full opacity-50"
          variants={createPulseVariants(1)}
          initial="initial"
          animate="animate"
          style={{ willChange: 'transform' }}
        />

        <motion.div
          className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-neon-purple rounded-full opacity-45"
          variants={createPulseVariants(2)}
          initial="initial"
          animate="animate"
          style={{ willChange: 'transform' }}
        />
      </div>
    </div>
  );
};

export default OrbitingIcons;
