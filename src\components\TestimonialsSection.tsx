import React from 'react';
import ScrollReveal from './ScrollReveal';
import { Star, MessageSquareQuote } from 'lucide-react';

const TestimonialsSection: React.FC = () => {

  const testimonials = [
    {
      text: "Econic Media transformed our product photography without requiring us to ship anything. Their ability to create studio-quality visuals from our basic photos increased our conversion rate by 28% within the first month. Their attention to detail is exceptional.",
      author: "<PERSON>",
      position: "E-commerce Director, NutriBlend",
      rating: 5,
      service: "Product Photography"
    },
    {
      text: "We hired Econic Media for our social media ad creatives and website design. Their comprehensive approach to our brand's visual identity helped us achieve a 42% increase in engagement and a significant boost in qualified leads. Their landing page designs convert exceptionally well.",
      author: "<PERSON>",
      position: "Marketing Manager, TechSphere Solutions",
      rating: 5,
      service: "Social Media & Web Design"
    },
    {
      text: "Working with Econic Media on our branding and identity design was a game-changer for our startup. Their strategic approach to visual storytelling helped us stand out in a crowded market and attract investor interest. Our brand recognition metrics improved by 35% after the rebrand.",
      author: "<PERSON>",
      position: "Founder, Bloom Wellness",
      rating: 5,
      service: "Branding & Identity"
    },
  ];

  return (
    <section id="testimonials" className="section-padding relative overflow-hidden">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Enhanced gradient orb decorations optimized for darker background - Static */}
      <div className="absolute top-20 right-1/4 w-56 h-56 bg-gradient-ocean opacity-20 rounded-full blur-3xl z-5"></div>
      <div className="absolute bottom-20 left-1/4 w-64 h-64 bg-gradient-sunset opacity-18 rounded-full blur-3xl z-5"></div>
      <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-gradient-aurora opacity-12 rounded-full blur-3xl z-5"></div>



      <div className="container max-w-7xl mx-auto relative z-20">
        <ScrollReveal>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-center mb-16 leading-tight">
            <span className="text-gradient-aurora drop-shadow-glow">Real Results from Real Clients</span>
          </h2>
        </ScrollReveal>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <ScrollReveal key={index} delay={index * 200}>
              <div className="group relative overflow-hidden rounded-2xl luxury-card transition-all duration-500 ease-out h-full flex flex-col p-8 hover:border-premium-gold/40 hover:shadow-[0_16px_48px_-8px_rgba(255,215,0,0.3)]">
                {/* Premium glow effects */}
                <div className="absolute -inset-2 rounded-3xl bg-gradient-aurora opacity-0 group-hover:opacity-15 blur-xl z-0 transition-opacity duration-500"></div>
                <div className="absolute -inset-1 rounded-2xl bg-gradient-luxury opacity-0 group-hover:opacity-8 blur-lg z-0 transition-opacity duration-500"></div>

                {/* Testimonial quote icon */}
                <MessageSquareQuote className="absolute top-6 left-6 h-8 w-8 text-premium-gold/30 relative z-10" />

                {/* Star rating */}
                <div className="flex mb-4 relative z-10">
                  {Array.from({ length: testimonial.rating }).map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-premium-gold" fill="#FFD700" />
                  ))}
                </div>

                {/* Testimonial text */}
                <blockquote className="text-foreground/90 mb-6 italic leading-relaxed relative z-10 flex-grow text-lg">
                  "{testimonial.text}"
                </blockquote>

                {/* Author info and service tag */}
                <div className="mt-auto relative z-10">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold text-premium-platinum text-lg">{testimonial.author}</p>
                      <p className="text-sm text-foreground/70">{testimonial.position}</p>
                    </div>
                    <div className="text-xs text-premium-gold font-medium bg-premium-gold/10 px-3 py-1 rounded-full border border-premium-gold/20">
                      {testimonial.service}
                    </div>
                  </div>
                </div>

                {/* Floating premium accent elements */}
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-premium-gold rounded-full opacity-0 group-hover:opacity-60 z-20 transition-opacity duration-500"></div>
                <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-premium-silver rounded-full opacity-0 group-hover:opacity-40 z-20 transition-opacity duration-500"></div>
              </div>
            </ScrollReveal>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
